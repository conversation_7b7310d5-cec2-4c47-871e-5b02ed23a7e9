#!/usr/bin/env python3
"""
Test the user's payload with the vital signs monitoring system
"""

import json
import sys
import os
from datetime import datetime

# Add tools path to system path
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), 'tools')))

from tools.tools_monitor_vital_signs import monitor_vital_signs

def process_vital_signs_test(vital_signs):
    """Test version of process_vital_signs function"""
    try:
        # Format data for the tool
        vital_signs_json = json.dumps({"data": vital_signs})
        print(f"Processing vital signs JSON: {vital_signs_json}")

        # Use the vital sign monitoring tool
        result = monitor_vital_signs(vital_signs_json)
        print(f"\n=== DETAILED ANALYSIS ===")
        print(result)

        # Check for abnormal patterns (additional alerts)
        alerts = []
        severity = "Normal"
        
        # Convert values to float for comparison, handling non-numeric values gracefully
        def safe_float_get(key: str) -> float:
            value = vital_signs.get(key)
            if value is None:
                return None
            try:
                return float(value)
            except (ValueError, TypeError):
                print(f"Could not convert {key} value '{value}' to float")
                return None
        
        glucose = safe_float_get("Glucose")
        if glucose and glucose > 140:
            alerts.append("⚠️ High glucose levels detected, Possible hyperglycemia. Consider consulting a doctor.")
            severity = "Critical"
            
        spo2 = safe_float_get("SpO2")
        if spo2 and spo2 < 95:
            alerts.append("⚠️ Low SpO2 levels detected, Possible hypoxemia. Ensure proper ventilation.")
            severity = "Critical"
            
        heart_rate = safe_float_get("ECG (Heart Rate)") or safe_float_get("Heart_Rate")
        if heart_rate and heart_rate > 100:
            alerts.append("⚠️ High heart rate detected. Practice stress management.")
            severity = "Caution"
            
        temperature = safe_float_get("Temperature")
        if temperature and temperature > 37.5:
            alerts.append("⚠️ Fever detected. Stay hydrated and consult a doctor if it persists.")
            severity = "Caution"

        alert_text = "\n".join(alerts) if alerts else "✅ No abnormal patterns detected."

        return {
            "success": True,
            "message": "Vital signs processed successfully",
            "analysis": result,
            "alerts": alert_text,
            "severity": severity,
            "suggest_consultation": len(alerts) > 0,
            "recommendation": "Please consult your doctor." if severity == "Critical" else "Continue monitoring regularly.",
            "timestamp": datetime.now().isoformat()
        }
    except Exception as e:
        error_msg = f"Failed to process vital signs: {str(e)}"
        print(f"ERROR: {error_msg}")
        return {
            "success": False,
            "error": error_msg,
            "message": "Failed to process vital signs data"
        }

def test_user_payload():
    """Test the user's specific payload"""
    
    user_payload = {
        "user_id": "test08",
        "vital_signs": {
            "Glucose": 110,
            "SpO2": 92,
            "Temperature": 38.2,
            "ECG (Heart Rate)": 102,
            "Blood Pressure (Systolic)": 130,
            "Blood Pressure (Diastolic)": 85,
            "Waist Circumference": 98,
            "Weight (BMI)": 26.5,
            "Hepatitis B": "Positive",
            "HIV": "Positive",
            "Malaria": "Positive",
            "Widal Test": "Positive",
            "Lung Capacity": 2.1
        }
    }
    
    print("=== TESTING USER PAYLOAD ===")
    print(f"User ID: {user_payload['user_id']}")
    print(f"Vital Signs Data: {json.dumps(user_payload['vital_signs'], indent=2)}")
    
    # Process the vital signs
    result = process_vital_signs_test(user_payload['vital_signs'])
    
    print(f"\n=== ENDPOINT RESPONSE ===")
    print(json.dumps(result, indent=2))
    
    # Summary
    print(f"\n=== SUMMARY ===")
    if result.get("success"):
        print("✅ Processing: SUCCESSFUL")
        print(f"🚨 Severity Level: {result.get('severity', 'Unknown')}")
        print(f"🩺 Consultation Recommended: {'YES' if result.get('suggest_consultation') else 'NO'}")
        print(f"📋 Recommendation: {result.get('recommendation', 'N/A')}")
        
        # Count alerts
        alerts = result.get('alerts', '')
        alert_count = len([line for line in alerts.split('\n') if line.strip() and '⚠️' in line])
        print(f"⚠️ Alert Count: {alert_count}")
        
        if alert_count > 0:
            print(f"🚨 ALERTS:")
            for line in alerts.split('\n'):
                if line.strip() and '⚠️' in line:
                    print(f"   {line.strip()}")
    else:
        print("❌ Processing: FAILED")
        print(f"Error: {result.get('error', 'Unknown error')}")

if __name__ == "__main__":
    print("Testing User's Vital Signs Payload")
    print("=" * 60)
    test_user_payload()
    print("=" * 60)
    print("Test completed!")
