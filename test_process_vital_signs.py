#!/usr/bin/env python3
"""
Test script for the process_vital_signs function in agent_server.py
"""

import json
import sys
import os
import logging

# Add current directory to path to import from agent_server
sys.path.append(os.path.dirname(__file__))

# Set up logging
logging.basicConfig(level=logging.INFO)

# Import the function we want to test
try:
    from agent_server import process_vital_signs
    print("✅ Successfully imported process_vital_signs from agent_server")
except ImportError as e:
    print(f"❌ Failed to import process_vital_signs: {e}")
    sys.exit(1)

def test_process_vital_signs():
    """Test the process_vital_signs function directly"""
    
    # Test 1: Normal values
    print("\n=== Test 1: Normal Vital Signs ===")
    test_data_1 = {
        "Glucose": 95.0,
        "SpO2": 98.0,
        "ECG (Heart Rate)": 72.0,
        "Temperature": 36.8,
        "Blood Pressure (Systolic)": 120.0,
        "Blood Pressure (Diastolic)": 80.0,
        "Weight (BMI)": 22.5
    }
    
    try:
        result_1 = process_vital_signs(test_data_1)
        if "error" in result_1:
            print(f"❌ Test 1 FAILED: {result_1['error']}")
        else:
            print("✅ Test 1 PASSED")
            print(f"Analysis: {result_1.get('analysis', 'N/A')[:100]}...")
            print(f"Alerts: {result_1.get('alerts', 'N/A')}")
            print(f"Severity: {result_1.get('severity', 'N/A')}")
            print(f"Suggest Consultation: {result_1.get('suggest_consultation', 'N/A')}")
    except Exception as e:
        print(f"❌ Test 1 FAILED with exception: {e}")
    
    # Test 2: Abnormal values
    print("\n=== Test 2: Abnormal Vital Signs ===")
    test_data_2 = {
        "Glucose": 180.0,  # High
        "SpO2": 90.0,      # Low
        "ECG (Heart Rate)": 110.0,  # High
        "Temperature": 38.5,  # High (fever)
        "Blood Pressure (Systolic)": 150.0,  # High
        "Blood Pressure (Diastolic)": 95.0   # High
    }
    
    try:
        result_2 = process_vital_signs(test_data_2)
        if "error" in result_2:
            print(f"❌ Test 2 FAILED: {result_2['error']}")
        else:
            print("✅ Test 2 PASSED")
            print(f"Analysis: {result_2.get('analysis', 'N/A')[:100]}...")
            print(f"Alerts: {result_2.get('alerts', 'N/A')}")
            print(f"Severity: {result_2.get('severity', 'N/A')}")
            print(f"Suggest Consultation: {result_2.get('suggest_consultation', 'N/A')}")
    except Exception as e:
        print(f"❌ Test 2 FAILED with exception: {e}")
    
    # Test 3: Mixed data types (including strings)
    print("\n=== Test 3: Mixed Data Types ===")
    test_data_3 = {
        "Glucose": "95",
        "SpO2": 98.0,
        "ECG (Heart Rate)": "72",
        "Temperature": 36.8,
        "Malaria": "Negative",
        "Widal Test": "Negative",
        "Weight (BMI)": "22.5"
    }
    
    try:
        result_3 = process_vital_signs(test_data_3)
        if "error" in result_3:
            print(f"❌ Test 3 FAILED: {result_3['error']}")
        else:
            print("✅ Test 3 PASSED")
            print(f"Analysis: {result_3.get('analysis', 'N/A')[:100]}...")
            print(f"Alerts: {result_3.get('alerts', 'N/A')}")
            print(f"Severity: {result_3.get('severity', 'N/A')}")
            print(f"Suggest Consultation: {result_3.get('suggest_consultation', 'N/A')}")
    except Exception as e:
        print(f"❌ Test 3 FAILED with exception: {e}")
    
    # Test 4: Invalid data types
    print("\n=== Test 4: Invalid Data Types ===")
    test_data_4 = {
        "Glucose": "invalid",
        "SpO2": "not_a_number",
        "Malaria": "Maybe",  # Invalid test result
        "Temperature": None
    }
    
    try:
        result_4 = process_vital_signs(test_data_4)
        if "error" in result_4:
            print(f"❌ Test 4 FAILED: {result_4['error']}")
        else:
            print("✅ Test 4 PASSED")
            print(f"Analysis: {result_4.get('analysis', 'N/A')[:100]}...")
            print(f"Alerts: {result_4.get('alerts', 'N/A')}")
            print(f"Severity: {result_4.get('severity', 'N/A')}")
            print(f"Suggest Consultation: {result_4.get('suggest_consultation', 'N/A')}")
    except Exception as e:
        print(f"❌ Test 4 FAILED with exception: {e}")

if __name__ == "__main__":
    print("Testing process_vital_signs Function Directly")
    print("=" * 50)
    test_process_vital_signs()
    print("\n" + "=" * 50)
    print("Testing completed!")
