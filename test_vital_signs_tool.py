#!/usr/bin/env python3
"""
Test script for the vital signs monitoring tool directly
"""

import json
import sys
import os

# Add tools path to system path
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), 'tools')))

from tools.tools_monitor_vital_signs import monitor_vital_signs

def test_tool_directly():
    """Test the monitor_vital_signs function directly"""
    
    # Test 1: Normal values
    print("=== Test 1: Normal Vital Signs ===")
    test_data_1 = {
        "data": {
            "Glucose": 95.0,
            "SpO2": 98.0,
            "ECG (Heart Rate)": 72.0,
            "Temperature": 36.8,
            "Blood Pressure (Systolic)": 120.0,
            "Blood Pressure (Diastolic)": 80.0,
            "Weight (BMI)": 22.5
        }
    }
    
    try:
        result_1 = monitor_vital_signs(json.dumps(test_data_1))
        print("✅ Test 1 PASSED")
        print(f"Result:\n{result_1}\n")
    except Exception as e:
        print(f"❌ Test 1 FAILED: {e}\n")
    
    # Test 2: Abnormal values
    print("=== Test 2: Abnormal Vital Signs ===")
    test_data_2 = {
        "data": {
            "Glucose": 180.0,  # High
            "SpO2": 90.0,      # Low
            "ECG (Heart Rate)": 110.0,  # High
            "Temperature": 38.5,  # High (fever)
            "Blood Pressure (Systolic)": 150.0,  # High
            "Blood Pressure (Diastolic)": 95.0   # High
        }
    }
    
    try:
        result_2 = monitor_vital_signs(json.dumps(test_data_2))
        print("✅ Test 2 PASSED")
        print(f"Result:\n{result_2}\n")
    except Exception as e:
        print(f"❌ Test 2 FAILED: {e}\n")
    
    # Test 3: Mixed data types (including strings)
    print("=== Test 3: Mixed Data Types ===")
    test_data_3 = {
        "data": {
            "Glucose": "95",
            "SpO2": 98.0,
            "ECG (Heart Rate)": "72",
            "Temperature": 36.8,
            "Malaria": "Negative",
            "Widal Test": "Negative",
            "Weight (BMI)": "22.5"
        }
    }
    
    try:
        result_3 = monitor_vital_signs(json.dumps(test_data_3))
        print("✅ Test 3 PASSED")
        print(f"Result:\n{result_3}\n")
    except Exception as e:
        print(f"❌ Test 3 FAILED: {e}\n")
    
    # Test 4: Invalid data types
    print("=== Test 4: Invalid Data Types ===")
    test_data_4 = {
        "data": {
            "Glucose": "invalid",
            "SpO2": "not_a_number",
            "Malaria": "Maybe",  # Invalid test result
            "Temperature": None
        }
    }
    
    try:
        result_4 = monitor_vital_signs(json.dumps(test_data_4))
        print("✅ Test 4 PASSED")
        print(f"Result:\n{result_4}\n")
    except Exception as e:
        print(f"❌ Test 4 FAILED: {e}\n")

if __name__ == "__main__":
    print("Testing Vital Signs Monitoring Tool Directly")
    print("=" * 50)
    test_tool_directly()
    print("=" * 50)
    print("Testing completed!")
