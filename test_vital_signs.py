#!/usr/bin/env python3
"""
Test script for the vital signs monitoring endpoint
"""

import requests
import json

# Test data
test_data = {
    "user_id": "test_user_123",
    "vital_signs": {
        "Glucose": 95.0,
        "SpO2": 98.0,
        "ECG (Heart Rate)": 72.0,
        "Temperature": 36.8,
        "Blood Pressure (Systolic)": 120.0,
        "Blood Pressure (Diastolic)": 80.0,
        "Weight (BMI)": 22.5
    }
}

# Test with abnormal values
test_data_abnormal = {
    "user_id": "test_user_456",
    "vital_signs": {
        "Glucose": 180.0,  # High
        "SpO2": 90.0,      # Low
        "ECG (Heart Rate)": 110.0,  # High
        "Temperature": 38.5,  # High (fever)
        "Blood Pressure (Systolic)": 150.0,  # High
        "Blood Pressure (Diastolic)": 95.0   # High
    }
}

# Test with mixed data types (including strings)
test_data_mixed = {
    "user_id": "test_user_789",
    "vital_signs": {
        "Glucose": "95",
        "SpO2": 98.0,
        "ECG (Heart Rate)": "72",
        "Temperature": 36.8,
        "Malaria": "Negative",
        "Widal Test": "Negative",
        "Weight (BMI)": "22.5"
    }
}

def test_endpoint(url, data, test_name):
    """Test the vital signs endpoint"""
    print(f"\n=== {test_name} ===")
    print(f"Request data: {json.dumps(data, indent=2)}")
    
    try:
        response = requests.post(url, json=data, timeout=30)
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"Response: {json.dumps(result, indent=2)}")
            
            if result.get("success"):
                print("✅ Test PASSED")
            else:
                print("❌ Test FAILED - Success flag is False")
                if "error" in result:
                    print(f"Error: {result['error']}")
        else:
            print(f"❌ Test FAILED - HTTP {response.status_code}")
            print(f"Response: {response.text}")
            
    except requests.exceptions.RequestException as e:
        print(f"❌ Test FAILED - Request error: {e}")
    except Exception as e:
        print(f"❌ Test FAILED - Unexpected error: {e}")

if __name__ == "__main__":
    # Assuming the server is running on localhost:8000
    base_url = "http://localhost:8000"
    endpoint_url = f"{base_url}/vital-signs"
    
    print("Testing Vital Signs Monitoring Endpoint")
    print("=" * 50)
    
    # Test 1: Normal values
    test_endpoint(endpoint_url, test_data, "Normal Vital Signs")
    
    # Test 2: Abnormal values
    test_endpoint(endpoint_url, test_data_abnormal, "Abnormal Vital Signs")
    
    # Test 3: Mixed data types
    test_endpoint(endpoint_url, test_data_mixed, "Mixed Data Types")
    
    print("\n" + "=" * 50)
    print("Testing completed!")
