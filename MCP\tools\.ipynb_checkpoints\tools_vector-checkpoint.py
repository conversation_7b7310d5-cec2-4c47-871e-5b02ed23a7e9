import requests
from langchain.tools import Tool

def call_mcp_vector_search(query: str) -> str:
    try:
        response = requests.post(
            "http://localhost:8000/query",
            json={"user_id": "Dr Deuce", "query": query},
            timeout=15
        )
        data = response.json()

        # Return the response or fallback message
        return data.get("response", "No relevant information found.")
    
    except Exception:
        return "⚠️ Could not contact the MCP server. Please try again."

vector_search_tool = Tool(
    name="vector_search_tool",
    func=call_mcp_vector_search,
    description="Retrieves medical or health-related info from the vector store."
)



