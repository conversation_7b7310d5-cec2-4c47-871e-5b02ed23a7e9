#!/usr/bin/env python3
"""
Simple test script for vital signs processing logic
"""

import json
import sys
import os
from datetime import datetime

# Add tools path to system path
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), 'tools')))

from tools.tools_monitor_vital_signs import monitor_vital_signs

def process_vital_signs_test(vital_signs):
    """Test version of process_vital_signs function"""
    try:
        # Format data for the tool
        vital_signs_json = json.dumps({"data": vital_signs})
        print(f"Processing vital signs: {vital_signs_json}")

        # Use the vital sign monitoring tool
        result = monitor_vital_signs(vital_signs_json)
        print(f"Vital signs monitoring result: {result}")

        # Check for abnormal patterns
        alerts = []
        severity = "Normal"
        
        # Convert values to float for comparison, handling non-numeric values gracefully
        def safe_float_get(key: str) -> float:
            value = vital_signs.get(key)
            if value is None:
                return None
            try:
                return float(value)
            except (ValueError, TypeError):
                print(f"Could not convert {key} value '{value}' to float")
                return None
        
        glucose = safe_float_get("Glucose")
        if glucose and glucose > 140:
            alerts.append("⚠️ High glucose levels detected, Possible hyperglycemia. Consider consulting a doctor.")
            severity = "Critical"
            
        spo2 = safe_float_get("SpO2")
        if spo2 and spo2 < 95:
            alerts.append("⚠️ Low SpO2 levels detected, Possible hypoxemia. Ensure proper ventilation.")
            severity = "Critical"
            
        heart_rate = safe_float_get("ECG (Heart Rate)") or safe_float_get("Heart_Rate")
        if heart_rate and heart_rate > 100:
            alerts.append("⚠️ High heart rate detected. Practice stress management.")
            severity = "Caution"
            
        temperature = safe_float_get("Temperature")
        if temperature and temperature > 37.5:
            alerts.append("⚠️ Fever detected. Stay hydrated and consult a doctor if it persists.")
            severity = "Caution"

        alert_text = "\n".join(alerts) if alerts else "✅ No abnormal patterns detected."

        return {
            "analysis": result,
            "alerts": alert_text,
            "suggest_consultation": len(alerts) > 0,
            "recommendation": "Please consult your doctor." if severity == "Critical" else "Continue monitoring regularly.",
            "severity": severity
        }
    except Exception as e:
        error_msg = f"Failed to process vital signs: {str(e)}"
        print(error_msg)
        return {"error": error_msg}

def run_tests():
    """Run the tests"""
    
    # Test 1: Normal values
    print("\n=== Test 1: Normal Vital Signs ===")
    test_data_1 = {
        "Glucose": 95.0,
        "SpO2": 98.0,
        "ECG (Heart Rate)": 72.0,
        "Temperature": 36.8,
        "Blood Pressure (Systolic)": 120.0,
        "Blood Pressure (Diastolic)": 80.0,
        "Weight (BMI)": 22.5
    }
    
    result_1 = process_vital_signs_test(test_data_1)
    if "error" in result_1:
        print(f"❌ Test 1 FAILED: {result_1['error']}")
    else:
        print("✅ Test 1 PASSED")
        print(f"Alerts: {result_1.get('alerts', 'N/A')}")
        print(f"Severity: {result_1.get('severity', 'N/A')}")
        print(f"Suggest Consultation: {result_1.get('suggest_consultation', 'N/A')}")
    
    # Test 2: Abnormal values
    print("\n=== Test 2: Abnormal Vital Signs ===")
    test_data_2 = {
        "Glucose": 180.0,  # High
        "SpO2": 90.0,      # Low
        "ECG (Heart Rate)": 110.0,  # High
        "Temperature": 38.5,  # High (fever)
        "Blood Pressure (Systolic)": 150.0,  # High
        "Blood Pressure (Diastolic)": 95.0   # High
    }
    
    result_2 = process_vital_signs_test(test_data_2)
    if "error" in result_2:
        print(f"❌ Test 2 FAILED: {result_2['error']}")
    else:
        print("✅ Test 2 PASSED")
        print(f"Alerts: {result_2.get('alerts', 'N/A')}")
        print(f"Severity: {result_2.get('severity', 'N/A')}")
        print(f"Suggest Consultation: {result_2.get('suggest_consultation', 'N/A')}")
    
    # Test 3: Mixed data types
    print("\n=== Test 3: Mixed Data Types ===")
    test_data_3 = {
        "Glucose": "95",
        "SpO2": 98.0,
        "ECG (Heart Rate)": "72",
        "Temperature": 36.8,
        "Malaria": "Negative",
        "Widal Test": "Negative",
        "Weight (BMI)": "22.5"
    }
    
    result_3 = process_vital_signs_test(test_data_3)
    if "error" in result_3:
        print(f"❌ Test 3 FAILED: {result_3['error']}")
    else:
        print("✅ Test 3 PASSED")
        print(f"Alerts: {result_3.get('alerts', 'N/A')}")
        print(f"Severity: {result_3.get('severity', 'N/A')}")
        print(f"Suggest Consultation: {result_3.get('suggest_consultation', 'N/A')}")

if __name__ == "__main__":
    print("Testing Vital Signs Processing Logic")
    print("=" * 50)
    run_tests()
    print("\n" + "=" * 50)
    print("Testing completed!")
